package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateOrderDomainRequest struct {
	Name        string  `json:"name" validate:"required,min=2,max=50"`
	IsAvailable bool    `json:"is_available"`
	OrderID     uint64  `json:"order_id" validate:"required"`
	Nameserver1 *string `json:"nameserver_1,omitempty"`
	Nameserver2 *string `json:"nameserver_2,omitempty"`
}

type UpdateOrderDomainRequest struct {
	Name        string  `json:"name" validate:"required,min=2,max=50"`
	IsAvailable bool    `json:"is_available"`
	OrderID     uint64  `json:"order_id" validate:"required"`
	Nameserver1 *string `json:"nameserver_1,omitempty"`
	Nameserver2 *string `json:"nameserver_2,omitempty"`
}

type UpdateOrderDomainAvailabilityRequest struct {
	IsAvailable bool `json:"is_available"`
}

type GetByNamespaceDomainRequest struct {
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
	Name        string `json:"name" validate:"required,min=1,max=50"`
}

type OrderDomainListItemResponse struct {
	ID          uint64                 `json:"id"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Name        string                 `json:"name"`
	IsAvailable bool                   `json:"is_available"`
	OrderID     uint64                 `json:"order_id"`
	Nameserver1 *string                `json:"nameserver_1,omitempty"`
	Nameserver2 *string                `json:"nameserver_2,omitempty"`
	Order       *OrderRelationResponse `json:"order,omitempty"`
}

type OrderDomainDetailResponse struct {
	ID          uint64                 `json:"id"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Name        string                 `json:"name"`
	IsAvailable bool                   `json:"is_available"`
	OrderID     uint64                 `json:"order_id"`
	Nameserver1 *string                `json:"nameserver_1,omitempty"`
	Nameserver2 *string                `json:"nameserver_2,omitempty"`
	Order       *OrderRelationResponse `json:"order,omitempty"`
}

type OrderDomainRelationResponse struct {
	ID          uint64  `json:"id"`
	Name        string  `json:"name"`
	IsAvailable bool    `json:"is_available"`
	Nameserver1 *string `json:"nameserver_1,omitempty"`
	Nameserver2 *string `json:"nameserver_2,omitempty"`
}

// Convert response

func ToOrderDomainListItemDTO(d *domain.OrderDomain) *OrderDomainListItemResponse {
	if d == nil {
		return nil
	}

	return &OrderDomainListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		IsAvailable: d.IsAvailable,
		OrderID:     d.OrderID,
		Nameserver1: d.Nameserver1,
		Nameserver2: d.Nameserver2,
		Order:       ToOrderRelationDTO(d.Order),
	}
}

func ToOrderDomainDetailDTO(d *domain.OrderDomain) *OrderDomainDetailResponse {
	if d == nil {
		return nil
	}

	return &OrderDomainDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		IsAvailable: d.IsAvailable,
		OrderID:     d.OrderID,
		Nameserver1: d.Nameserver1,
		Nameserver2: d.Nameserver2,
		Order:       ToOrderRelationDTO(d.Order),
	}
}

func ToOrderDomainRelationDTO(d *domain.OrderDomain) *OrderDomainRelationResponse {
	if d == nil {
		return nil
	}
	return &OrderDomainRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		IsAvailable: d.IsAvailable,
		Nameserver1: d.Nameserver1,
		Nameserver2: d.Nameserver2,
	}
}
