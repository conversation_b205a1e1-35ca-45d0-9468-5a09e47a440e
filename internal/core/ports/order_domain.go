package ports

import "ops-api/internal/core/domain"

// OrderDomain<PERSON>ilter represents filters for querying order domains
type OrderDomainFilter struct {
	Name        *string
	IsAvailable *bool
	OrderID     *uint64
}

type OrderDomainRepository interface {
	Insert(orderDomain *domain.OrderDomain) error
	FindAll(filter *OrderDomainFilter) ([]*domain.OrderDomain, error)
	FindByID(id uint64) (*domain.OrderDomain, error)
	Update(orderDomain *domain.OrderDomain) error
	Delete(id uint64) error
}

type OrderDomainService interface {
	Create(name string, orderID uint64, nameserver1, nameserver2 *string) (*domain.OrderDomain, error)
	GetAll(filter *OrderDomainFilter) ([]*domain.OrderDomain, error)
	GetByID(id uint64) (*domain.OrderDomain, error)
	GetByNamespaceDomain(namespaceID uint64, name string) ([]*domain.OrderDomain, error)
	UpdateAvailability(id uint64, isAvailable bool) (*domain.OrderDomain, error)
	Delete(id uint64) error
}
