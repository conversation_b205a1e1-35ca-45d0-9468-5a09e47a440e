package domain

type OrderDomain struct {
	BaseModel
	Name        string  `json:"name" gorm:"not null"`
	IsAvailable bool    `json:"is_available" gorm:"not null default:false"`
	OrderID     uint64  `json:"order_id" gorm:"not null"`
	Nameserver1 *string `json:"nameserver_1,omitempty" gorm:"type:varchar(255)"`
	Nameserver2 *string `json:"nameserver_2,omitempty" gorm:"type:varchar(255)"`

	// Relationships
	Order *Order `json:"order,omitempty" gorm:"foreignKey:OrderID"`
}
